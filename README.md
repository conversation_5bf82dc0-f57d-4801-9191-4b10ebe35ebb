# 在线翻译工具 - Google Translate API

这是一个基于Google Translate API的在线翻译工具，支持自动语言检测和100+种语言互译。

## 🚀 功能特点

- **智能语言检测**：自动识别输入文本的语言类型
- **多语言支持**：支持100+种语言互译
- **实时翻译**：快速准确的翻译响应
- **响应式设计**：完美适配各种设备屏幕
- **快捷操作**：支持键盘快捷键和一键操作
- **错误处理**：完善的错误提示和重试机制

## 📋 使用说明

### 1. 获取Google Translate API密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Cloud Translation API"
4. 创建API密钥（建议限制API密钥的使用范围）
5. 将API密钥替换到 `translate.js` 文件中的 `CONFIG.API_KEY`

### 2. 配置API密钥

在 `translate.js` 文件中找到以下配置：

```javascript
const CONFIG = {
    API_KEY: 'YOUR_GOOGLE_TRANSLATE_API_KEY', // 替换为您的API密钥
    BASE_URL: 'https://translation.googleapis.com/language/translate/v2',
    DETECT_URL: 'https://translation.googleapis.com/language/translate/v2/detect',
    LANGUAGES_URL: 'https://translation.googleapis.com/language/translate/v2/languages'
};
```

### 3. 部署方式

#### 方式一：本地运行
1. 将所有文件放在同一目录下
2. 使用本地服务器运行（如Live Server、Python HTTP Server等）
3. 打开浏览器访问 `translate.html`

#### 方式二：Web服务器部署
1. 将文件上传到Web服务器
2. 确保服务器支持HTTPS（Google API要求）
3. 访问部署的URL

### 4. CORS问题解决

由于浏览器的CORS限制，直接从前端调用Google API可能会失败。推荐的解决方案：

#### 方案一：使用代理服务器（推荐）

创建一个后端代理服务，例如使用Node.js：

```javascript
// server.js
const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
app.use(cors());
app.use(express.json());

const API_KEY = 'YOUR_GOOGLE_TRANSLATE_API_KEY';

app.post('/api/translate', async (req, res) => {
    try {
        const { text, source, target } = req.body;
        const response = await axios.get('https://translation.googleapis.com/language/translate/v2', {
            params: {
                q: text,
                source: source,
                target: target,
                key: API_KEY
            }
        });
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/detect', async (req, res) => {
    try {
        const { text } = req.body;
        const response = await axios.get('https://translation.googleapis.com/language/translate/v2/detect', {
            params: {
                q: text,
                key: API_KEY
            }
        });
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(3000, () => {
    console.log('代理服务器运行在端口 3000');
});
```

#### 方案二：使用浏览器扩展

如果您有浏览器扩展的开发权限，可以在manifest.json中添加权限：

```json
{
    "permissions": [
        "https://translation.googleapis.com/*"
    ]
}
```

#### 方案三：使用JSONP（有限支持）

Google Translate API支持JSONP回调，但功能有限。

## 🔧 自定义配置

### 添加新语言

在 `translate.js` 的 `LANGUAGES` 对象中添加新的语言代码和名称：

```javascript
const LANGUAGES = {
    // 现有语言...
    'new-lang': '新语言名称'
};
```

### 修改样式

所有样式都在 `translate.html` 的 `<style>` 标签中，您可以根据需要修改：

- 颜色主题
- 布局样式
- 响应式断点
- 动画效果

### 功能扩展

可以添加的功能：

1. **历史记录**：保存翻译历史
2. **收藏功能**：收藏常用翻译
3. **语音输入**：使用Web Speech API
4. **语音播放**：使用Text-to-Speech
5. **文件翻译**：支持文档翻译
6. **批量翻译**：支持多段文本翻译

## 🔒 安全注意事项

1. **API密钥保护**：
   - 不要在前端代码中暴露API密钥
   - 使用环境变量或后端代理
   - 限制API密钥的使用范围

2. **输入验证**：
   - 限制输入文本长度
   - 过滤恶意内容
   - 防止XSS攻击

3. **速率限制**：
   - 实现客户端速率限制
   - 监控API使用量
   - 设置合理的超时时间

## 📊 API使用限制

Google Translate API的使用限制：

- **免费额度**：每月500,000字符
- **速率限制**：每秒100请求
- **文本长度**：单次最大5,000字符
- **支持格式**：纯文本和HTML

## 🐛 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认API已启用
   - 检查网络连接

2. **CORS错误**
   - 使用后端代理
   - 检查浏览器控制台错误
   - 确认HTTPS部署

3. **翻译结果为空**
   - 检查源语言和目标语言设置
   - 确认输入文本格式正确
   - 查看API响应状态

### 调试模式

在浏览器控制台中启用调试：

```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');
```

## 📄 许可证

本项目采用 MIT 许可证。您可以自由使用、修改和分发此代码。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果您遇到问题或需要帮助，请：

1. 查看本README文档
2. 检查浏览器控制台错误
3. 提交Issue描述问题
4. 参考Google Translate API官方文档

---

**注意**：这是一个演示项目，在生产环境中使用时请确保遵循Google API的使用条款和最佳实践。
