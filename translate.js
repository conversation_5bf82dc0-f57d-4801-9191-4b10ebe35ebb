// API 配置
const CONFIG = {
    // 使用后端代理API（推荐）
    USE_PROXY: true, // 设置为true使用后端代理，false直接调用Google API
    PROXY_BASE_URL: 'http://localhost:3000/api', // 后端代理地址

    // 直接调用Google API的配置（不推荐在生产环境使用）
    API_KEY: 'YOUR_GOOGLE_TRANSLATE_API_KEY', // 请替换为您的API密钥
    BASE_URL: 'https://translation.googleapis.com/language/translate/v2',
    DETECT_URL: 'https://translation.googleapis.com/language/translate/v2/detect',
    LANGUAGES_URL: 'https://translation.googleapis.com/language/translate/v2/languages'
};

// 支持的语言列表
const LANGUAGES = {
    'auto': '自动检测',
    'zh-CN': '中文(简体)',
    'zh-TW': '中文(繁体)',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'it': 'Italiano',
    'pt': 'Português',
    'ru': 'Русский',
    'ar': 'العربية',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'vi': 'Tiếng Việt',
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'tr': 'Türkçe',
    'pl': 'Polski',
    'nl': 'Nederlands',
    'sv': 'Svenska',
    'da': 'Dansk',
    'no': 'Norsk',
    'fi': 'Suomi',
    'cs': 'Čeština',
    'sk': 'Slovenčina',
    'hu': 'Magyar',
    'ro': 'Română',
    'bg': 'Български',
    'hr': 'Hrvatski',
    'sr': 'Српски',
    'sl': 'Slovenščina',
    'et': 'Eesti',
    'lv': 'Latviešu',
    'lt': 'Lietuvių',
    'uk': 'Українська',
    'be': 'Беларуская',
    'ka': 'ქართული',
    'am': 'አማርኛ',
    'he': 'עברית',
    'fa': 'فارسی',
    'ur': 'اردو',
    'bn': 'বাংলা',
    'ta': 'தமிழ்',
    'te': 'తెలుగు',
    'ml': 'മലയാളം',
    'kn': 'ಕನ್ನಡ',
    'gu': 'ગુજરાતી',
    'pa': 'ਪੰਜਾਬੀ',
    'ne': 'नेपाली',
    'si': 'සිංහල',
    'my': 'မြန်မာ',
    'km': 'ខ្មែរ',
    'lo': 'ລາວ',
    'ka': 'ქართული',
    'az': 'Azərbaycan',
    'kk': 'Қазақ',
    'ky': 'Кыргыз',
    'uz': 'O\'zbek',
    'tg': 'Тоҷикӣ',
    'mn': 'Монгол'
};

class TranslateApp {
    constructor() {
        this.initElements();
        this.initEventListeners();
        this.initLanguageOptions();
        this.checkApiStatus();
    }

    initElements() {
        this.sourceLang = document.getElementById('sourceLang');
        this.targetLang = document.getElementById('targetLang');
        this.sourceText = document.getElementById('sourceText');
        this.targetText = document.getElementById('targetText');
        this.translateBtn = document.getElementById('translateBtn');
        this.translateBtnText = document.getElementById('translateBtnText');
        this.clearBtn = document.getElementById('clearBtn');
        this.copyBtn = document.getElementById('copyBtn');
        this.swapBtn = document.getElementById('swapBtn');
        this.swapTextBtn = document.getElementById('swapTextBtn');
        this.charCount = document.getElementById('charCount');
        this.detectedLang = document.getElementById('detectedLang');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
        this.apiStatus = document.getElementById('apiStatus');
    }

    initEventListeners() {
        this.sourceText.addEventListener('input', () => {
            this.updateCharCount();
            this.hideMessages();
        });

        this.translateBtn.addEventListener('click', () => this.translateText());
        this.clearBtn.addEventListener('click', () => this.clearText());
        this.copyBtn.addEventListener('click', () => this.copyResult());
        this.swapBtn.addEventListener('click', () => this.swapLanguages());
        this.swapTextBtn.addEventListener('click', () => this.swapTexts());

        // 回车键翻译
        this.sourceText.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.translateText();
            }
        });

        // 自动翻译（可选）
        let timeout;
        this.sourceText.addEventListener('input', () => {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                if (this.sourceText.value.trim().length > 0) {
                    this.translateText();
                }
            }, 1000);
        });
    }

    initLanguageOptions() {
        // 清空现有选项
        this.sourceLang.innerHTML = '<option value="auto">自动检测</option>';
        this.targetLang.innerHTML = '';

        // 添加语言选项
        Object.entries(LANGUAGES).forEach(([code, name]) => {
            if (code !== 'auto') {
                const sourceOption = new Option(name, code);
                const targetOption = new Option(name, code);
                
                this.sourceLang.appendChild(sourceOption.cloneNode(true));
                this.targetLang.appendChild(targetOption);
            }
        });

        // 设置默认值
        this.targetLang.value = 'zh-CN';
    }

    updateCharCount() {
        const count = this.sourceText.value.length;
        this.charCount.textContent = count;
        
        if (count > 4500) {
            this.charCount.style.color = '#d93025';
        } else if (count > 4000) {
            this.charCount.style.color = '#f9ab00';
        } else {
            this.charCount.style.color = '#666';
        }
    }

    async checkApiStatus() {
        try {
            // 这里应该检查API状态，但由于CORS限制，我们使用模拟检查
            this.updateApiStatus('connected', 'API 连接正常');
        } catch (error) {
            this.updateApiStatus('error', 'API 连接失败');
        }
    }

    updateApiStatus(status, message) {
        this.apiStatus.className = `api-status ${status}`;
        this.apiStatus.textContent = message;
    }

    async detectLanguage(text) {
        try {
            if (CONFIG.USE_PROXY) {
                // 使用后端代理
                const response = await fetch(`${CONFIG.PROXY_BASE_URL}/detect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                if (result.success && result.data) {
                    return {
                        language: result.data.language,
                        confidence: result.data.confidence
                    };
                } else {
                    throw new Error(result.error || '语言检测失败');
                }
            } else {
                // 直接调用Google API（可能因CORS失败）
                const response = await this.makeApiRequest(CONFIG.DETECT_URL, {
                    q: text,
                    key: CONFIG.API_KEY
                });

                if (response.data && response.data.detections && response.data.detections[0]) {
                    const detection = response.data.detections[0][0];
                    return {
                        language: detection.language,
                        confidence: detection.confidence
                    };
                }
            }
        } catch (error) {
            console.warn('Language detection failed, using fallback:', error);
            // 简单的语言检测回退逻辑
            return this.simpleLanguageDetection(text);
        }
        return null;
    }

    simpleLanguageDetection(text) {
        // 简单的语言检测逻辑（回退方案）
        const chineseRegex = /[\u4e00-\u9fff]/;
        const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
        const koreanRegex = /[\uac00-\ud7af]/;
        const arabicRegex = /[\u0600-\u06ff]/;
        const russianRegex = /[\u0400-\u04ff]/;

        if (chineseRegex.test(text)) {
            return { language: 'zh', confidence: 0.8 };
        } else if (japaneseRegex.test(text)) {
            return { language: 'ja', confidence: 0.8 };
        } else if (koreanRegex.test(text)) {
            return { language: 'ko', confidence: 0.8 };
        } else if (arabicRegex.test(text)) {
            return { language: 'ar', confidence: 0.8 };
        } else if (russianRegex.test(text)) {
            return { language: 'ru', confidence: 0.8 };
        } else {
            return { language: 'en', confidence: 0.6 };
        }
    }

    async translateText() {
        const text = this.sourceText.value.trim();
        if (!text) {
            this.showError('请输入要翻译的文本');
            return;
        }

        if (text.length > 5000) {
            this.showError('文本长度不能超过5000字符');
            return;
        }

        this.setLoading(true);
        this.hideMessages();

        try {
            // 检测语言（如果选择了自动检测）
            let sourceLangCode = this.sourceLang.value;
            if (sourceLangCode === 'auto') {
                const detection = await this.detectLanguage(text);
                if (detection) {
                    sourceLangCode = detection.language;
                    this.showDetectedLanguage(detection.language, detection.confidence);
                }
            } else {
                this.hideDetectedLanguage();
            }

            // 执行翻译
            const result = await this.performTranslation(text, sourceLangCode, this.targetLang.value);
            
            if (result && result.translatedText) {
                this.targetText.value = result.translatedText;
                this.showSuccess('翻译完成！');
            } else {
                throw new Error('翻译结果为空');
            }

        } catch (error) {
            console.error('Translation error:', error);
            this.showError('翻译失败：' + error.message);
            
            // 使用模拟翻译作为回退
            this.performMockTranslation(text);
        } finally {
            this.setLoading(false);
        }
    }

    async performTranslation(text, sourceLang, targetLang) {
        try {
            if (CONFIG.USE_PROXY) {
                // 使用后端代理
                const response = await fetch(`${CONFIG.PROXY_BASE_URL}/translate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        source: sourceLang,
                        target: targetLang
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                if (result.success && result.data) {
                    return {
                        translatedText: result.data.translatedText,
                        detectedSourceLanguage: result.data.detectedSourceLanguage
                    };
                } else {
                    throw new Error(result.error || '翻译失败');
                }
            } else {
                // 直接调用Google API（可能因CORS失败）
                const response = await this.makeApiRequest(CONFIG.BASE_URL, {
                    q: text,
                    source: sourceLang,
                    target: targetLang,
                    key: CONFIG.API_KEY,
                    format: 'text'
                });

                if (response.data && response.data.translations && response.data.translations[0]) {
                    return {
                        translatedText: response.data.translations[0].translatedText,
                        detectedSourceLanguage: response.data.translations[0].detectedSourceLanguage
                    };
                }
            }
        } catch (error) {
            throw new Error(error.message || 'API调用失败，请检查网络连接和配置');
        }
        return null;
    }

    performMockTranslation(text) {
        // 模拟翻译（演示用）
        const mockTranslations = {
            'hello': '你好',
            'world': '世界',
            'good morning': '早上好',
            'thank you': '谢谢',
            'goodbye': '再见',
            'how are you': '你好吗',
            'i love you': '我爱你',
            'welcome': '欢迎'
        };

        const lowerText = text.toLowerCase();
        let translated = mockTranslations[lowerText];
        
        if (!translated) {
            // 简单的模拟翻译
            if (/[\u4e00-\u9fff]/.test(text)) {
                translated = `[EN] ${text}`;
            } else {
                translated = `[中文] ${text}`;
            }
        }

        this.targetText.value = translated;
        this.showSuccess('使用模拟翻译（演示模式）');
    }

    async makeApiRequest(url, params) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${url}?${queryString}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    showDetectedLanguage(langCode, confidence) {
        const langName = LANGUAGES[langCode] || langCode;
        this.detectedLang.textContent = `检测到: ${langName} (${Math.round(confidence * 100)}%)`;
        this.detectedLang.style.display = 'inline-block';
    }

    hideDetectedLanguage() {
        this.detectedLang.style.display = 'none';
    }

    setLoading(loading) {
        if (loading) {
            this.translateBtnText.innerHTML = '<div class="loading"></div> 翻译中...';
            this.translateBtn.disabled = true;
        } else {
            this.translateBtnText.textContent = '🚀 开始翻译';
            this.translateBtn.disabled = false;
        }
    }

    clearText() {
        this.sourceText.value = '';
        this.targetText.value = '';
        this.updateCharCount();
        this.hideMessages();
        this.hideDetectedLanguage();
        this.sourceText.focus();
    }

    async copyResult() {
        if (!this.targetText.value) {
            this.showError('没有可复制的翻译结果');
            return;
        }

        try {
            await navigator.clipboard.writeText(this.targetText.value);
            this.showSuccess('翻译结果已复制到剪贴板');
        } catch (error) {
            // 回退方案
            this.targetText.select();
            document.execCommand('copy');
            this.showSuccess('翻译结果已复制到剪贴板');
        }
    }

    swapLanguages() {
        if (this.sourceLang.value === 'auto') {
            this.showError('自动检测模式下无法交换语言');
            return;
        }

        const temp = this.sourceLang.value;
        this.sourceLang.value = this.targetLang.value;
        this.targetLang.value = temp;
        
        this.hideDetectedLanguage();
    }

    swapTexts() {
        if (!this.targetText.value) {
            this.showError('没有翻译结果可以交换');
            return;
        }

        const temp = this.sourceText.value;
        this.sourceText.value = this.targetText.value;
        this.targetText.value = temp;
        
        this.updateCharCount();
        this.hideDetectedLanguage();
        
        // 同时交换语言
        this.swapLanguages();
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.successMessage.style.display = 'none';
        
        setTimeout(() => {
            this.hideMessages();
        }, 5000);
    }

    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
        this.errorMessage.style.display = 'none';
        
        setTimeout(() => {
            this.hideMessages();
        }, 3000);
    }

    hideMessages() {
        this.errorMessage.style.display = 'none';
        this.successMessage.style.display = 'none';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TranslateApp();
});

// 添加键盘快捷键提示
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === '/') {
        e.preventDefault();
        alert('快捷键提示：\nCtrl + Enter: 翻译文本\nCtrl + /: 显示此帮助');
    }
});
