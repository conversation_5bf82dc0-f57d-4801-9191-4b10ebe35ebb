// Google Translate API 代理服务器
// 用于解决前端CORS问题

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 从环境变量获取API密钥
const GOOGLE_API_KEY = process.env.GOOGLE_TRANSLATE_API_KEY;

if (!GOOGLE_API_KEY) {
    console.error('错误：请设置 GOOGLE_TRANSLATE_API_KEY 环境变量');
    process.exit(1);
}

// 中间件配置
app.use(helmet()); // 安全头
app.use(cors({
    origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public')); // 静态文件服务

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100次请求
    message: {
        error: '请求过于频繁，请稍后再试'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api/', limiter);

// Google Translate API 配置
const GOOGLE_TRANSLATE_CONFIG = {
    baseURL: 'https://translation.googleapis.com/language/translate/v2',
    timeout: 10000,
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
};

// 支持的语言列表缓存
let supportedLanguages = null;
let languagesCacheTime = null;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时

// 工具函数：验证语言代码
function isValidLanguageCode(code) {
    const validCodes = [
        'auto', 'zh-CN', 'zh-TW', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru',
        'ar', 'hi', 'th', 'vi', 'id', 'ms', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi',
        'cs', 'sk', 'hu', 'ro', 'bg', 'hr', 'sr', 'sl', 'et', 'lv', 'lt', 'uk', 'be',
        'ka', 'am', 'he', 'fa', 'ur', 'bn', 'ta', 'te', 'ml', 'kn', 'gu', 'pa', 'ne',
        'si', 'my', 'km', 'lo', 'az', 'kk', 'ky', 'uz', 'tg', 'mn'
    ];
    return validCodes.includes(code);
}

// 工具函数：清理和验证文本
function sanitizeText(text) {
    if (typeof text !== 'string') {
        throw new Error('文本必须是字符串类型');
    }
    
    // 移除潜在的恶意内容
    const cleaned = text
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
        .replace(/javascript:/gi, '') // 移除javascript协议
        .trim();
    
    if (cleaned.length === 0) {
        throw new Error('文本不能为空');
    }
    
    if (cleaned.length > 5000) {
        throw new Error('文本长度不能超过5000字符');
    }
    
    return cleaned;
}

// API路由：翻译文本
app.post('/api/translate', async (req, res) => {
    try {
        const { text, source = 'auto', target = 'en' } = req.body;
        
        // 验证输入
        if (!text) {
            return res.status(400).json({ error: '缺少必需的参数：text' });
        }
        
        const cleanText = sanitizeText(text);
        
        if (!isValidLanguageCode(source) || !isValidLanguageCode(target)) {
            return res.status(400).json({ error: '无效的语言代码' });
        }
        
        if (source === target && source !== 'auto') {
            return res.status(400).json({ error: '源语言和目标语言不能相同' });
        }
        
        // 调用Google Translate API
        const params = {
            q: cleanText,
            target: target,
            key: GOOGLE_API_KEY,
            format: 'text'
        };
        
        if (source !== 'auto') {
            params.source = source;
        }
        
        console.log(`翻译请求: ${source} -> ${target}, 文本长度: ${cleanText.length}`);
        
        const response = await axios.get(GOOGLE_TRANSLATE_CONFIG.baseURL, {
            params,
            timeout: GOOGLE_TRANSLATE_CONFIG.timeout
        });
        
        if (response.data && response.data.data && response.data.data.translations) {
            const translation = response.data.data.translations[0];
            
            res.json({
                success: true,
                data: {
                    translatedText: translation.translatedText,
                    detectedSourceLanguage: translation.detectedSourceLanguage || source,
                    originalText: cleanText,
                    sourceLanguage: source,
                    targetLanguage: target
                }
            });
            
            console.log(`翻译成功: ${source} -> ${target}`);
        } else {
            throw new Error('API返回数据格式错误');
        }
        
    } catch (error) {
        console.error('翻译错误:', error.message);
        
        if (error.response) {
            // Google API错误
            const status = error.response.status;
            const message = error.response.data?.error?.message || '翻译服务暂时不可用';
            
            res.status(status).json({
                success: false,
                error: message,
                code: status
            });
        } else if (error.code === 'ECONNABORTED') {
            // 超时错误
            res.status(408).json({
                success: false,
                error: '请求超时，请稍后重试'
            });
        } else {
            // 其他错误
            res.status(500).json({
                success: false,
                error: error.message || '内部服务器错误'
            });
        }
    }
});

// API路由：检测语言
app.post('/api/detect', async (req, res) => {
    try {
        const { text } = req.body;
        
        if (!text) {
            return res.status(400).json({ error: '缺少必需的参数：text' });
        }
        
        const cleanText = sanitizeText(text);
        
        console.log(`语言检测请求，文本长度: ${cleanText.length}`);
        
        const response = await axios.get(`${GOOGLE_TRANSLATE_CONFIG.baseURL}/detect`, {
            params: {
                q: cleanText,
                key: GOOGLE_API_KEY
            },
            timeout: GOOGLE_TRANSLATE_CONFIG.timeout
        });
        
        if (response.data && response.data.data && response.data.data.detections) {
            const detection = response.data.data.detections[0][0];
            
            res.json({
                success: true,
                data: {
                    language: detection.language,
                    confidence: detection.confidence,
                    isReliable: detection.isReliable || detection.confidence > 0.8
                }
            });
            
            console.log(`语言检测成功: ${detection.language} (置信度: ${detection.confidence})`);
        } else {
            throw new Error('API返回数据格式错误');
        }
        
    } catch (error) {
        console.error('语言检测错误:', error.message);
        
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || '语言检测服务暂时不可用';
            
            res.status(status).json({
                success: false,
                error: message,
                code: status
            });
        } else {
            res.status(500).json({
                success: false,
                error: error.message || '内部服务器错误'
            });
        }
    }
});

// API路由：获取支持的语言列表
app.get('/api/languages', async (req, res) => {
    try {
        // 检查缓存
        if (supportedLanguages && languagesCacheTime && 
            (Date.now() - languagesCacheTime) < CACHE_DURATION) {
            return res.json({
                success: true,
                data: supportedLanguages,
                cached: true
            });
        }
        
        console.log('获取支持的语言列表');
        
        const response = await axios.get(`${GOOGLE_TRANSLATE_CONFIG.baseURL}/languages`, {
            params: {
                key: GOOGLE_API_KEY,
                target: 'zh-CN' // 返回中文名称
            },
            timeout: GOOGLE_TRANSLATE_CONFIG.timeout
        });
        
        if (response.data && response.data.data && response.data.data.languages) {
            supportedLanguages = response.data.data.languages;
            languagesCacheTime = Date.now();
            
            res.json({
                success: true,
                data: supportedLanguages,
                cached: false
            });
            
            console.log(`获取到 ${supportedLanguages.length} 种支持的语言`);
        } else {
            throw new Error('API返回数据格式错误');
        }
        
    } catch (error) {
        console.error('获取语言列表错误:', error.message);
        
        // 返回默认语言列表作为回退
        const defaultLanguages = [
            { language: 'zh-CN', name: '中文(简体)' },
            { language: 'zh-TW', name: '中文(繁体)' },
            { language: 'en', name: 'English' },
            { language: 'ja', name: '日本語' },
            { language: 'ko', name: '한국어' },
            { language: 'es', name: 'Español' },
            { language: 'fr', name: 'Français' },
            { language: 'de', name: 'Deutsch' }
        ];
        
        res.json({
            success: true,
            data: defaultLanguages,
            fallback: true
        });
    }
});

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0'
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('未处理的错误:', error);
    res.status(500).json({
        success: false,
        error: '内部服务器错误'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: '接口不存在'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 翻译代理服务器启动成功`);
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`🔑 API密钥: ${GOOGLE_API_KEY ? '已配置' : '未配置'}`);
    console.log(`🌍 允许的源: ${process.env.ALLOWED_ORIGINS || '所有源'}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
