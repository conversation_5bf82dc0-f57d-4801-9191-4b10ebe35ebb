<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线翻译 - Google Translate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .translate-container {
            padding: 40px;
        }

        .language-selector {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
            flex-wrap: wrap;
        }

        .lang-select {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .lang-select:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }

        .swap-btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .swap-btn:hover {
            background: #3367d6;
            transform: rotate(180deg);
        }

        .text-areas {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .text-areas {
                grid-template-columns: 1fr;
            }
            
            .language-selector {
                flex-direction: column;
            }
        }

        .text-area-container {
            position: relative;
        }

        .text-area-label {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .detected-lang {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: normal;
        }

        .text-area {
            width: 100%;
            height: 200px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            font-size: 16px;
            line-height: 1.5;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .text-area:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }

        .text-area.readonly {
            background: #f8f9fa;
            cursor: default;
        }

        .char-count {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #4285f4;
            color: white;
        }

        .btn-primary:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .btn-secondary {
            background: #f1f3f4;
            color: #5f6368;
        }

        .btn-secondary:hover {
            background: #e8eaed;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-message {
            background: #fce8e6;
            color: #d93025;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #d93025;
        }

        .success-message {
            background: #e6f4ea;
            color: #137333;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #137333;
        }

        .features {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
        }

        .features h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .feature-item h4 {
            color: #4285f4;
            margin-bottom: 10px;
        }

        .api-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
        }

        .api-status.connected {
            background: #e6f4ea;
            color: #137333;
        }

        .api-status.error {
            background: #fce8e6;
            color: #d93025;
        }
    </style>
</head>
<body>
    <div class="api-status" id="apiStatus">API 状态检查中...</div>
    
    <div class="container">
        <div class="header">
            <h1>🌐 在线翻译</h1>
            <p>基于 Google Translate API 的智能翻译工具</p>
        </div>

        <div class="translate-container">
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            <div id="successMessage" class="success-message" style="display: none;"></div>

            <div class="language-selector">
                <select id="sourceLang" class="lang-select">
                    <option value="auto">自动检测</option>
                </select>
                
                <button class="swap-btn" id="swapBtn" title="交换语言">⇄</button>
                
                <select id="targetLang" class="lang-select">
                    <option value="zh-CN">中文(简体)</option>
                </select>
            </div>

            <div class="text-areas">
                <div class="text-area-container">
                    <div class="text-area-label">
                        源文本
                        <span id="detectedLang" class="detected-lang" style="display: none;"></span>
                    </div>
                    <textarea 
                        id="sourceText" 
                        class="text-area" 
                        placeholder="请输入要翻译的文本..."
                        maxlength="5000"
                    ></textarea>
                    <div class="char-count">
                        <span id="charCount">0</span>/5000
                    </div>
                </div>

                <div class="text-area-container">
                    <div class="text-area-label">翻译结果</div>
                    <textarea 
                        id="targetText" 
                        class="text-area readonly" 
                        placeholder="翻译结果将显示在这里..."
                        readonly
                    ></textarea>
                </div>
            </div>

            <div class="action-buttons">
                <button id="translateBtn" class="btn btn-primary">
                    <span id="translateBtnText">🚀 开始翻译</span>
                </button>
                <button id="clearBtn" class="btn btn-secondary">
                    🗑️ 清空文本
                </button>
                <button id="copyBtn" class="btn btn-secondary">
                    📋 复制结果
                </button>
                <button id="swapTextBtn" class="btn btn-secondary">
                    🔄 交换文本
                </button>
            </div>
        </div>

        <div class="features">
            <h3>✨ 功能特点</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🤖 智能检测</h4>
                    <p>自动识别输入文本的语言类型</p>
                </div>
                <div class="feature-item">
                    <h4>🌍 多语言支持</h4>
                    <p>支持100+种语言互译</p>
                </div>
                <div class="feature-item">
                    <h4>⚡ 实时翻译</h4>
                    <p>快速准确的翻译响应</p>
                </div>
                <div class="feature-item">
                    <h4>📱 响应式设计</h4>
                    <p>完美适配各种设备屏幕</p>
                </div>
            </div>
        </div>
    </div>

    <script src="translate.js"></script>
</body>
</html>
