# 部署指南

本文档详细说明如何部署Google Translate在线翻译工具。

## 📋 部署前准备

### 1. 获取Google Translate API密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Cloud Translation API"
4. 创建API密钥：
   - 转到 "APIs & Services" > "Credentials"
   - 点击 "Create Credentials" > "API Key"
   - 复制生成的API密钥
   - 建议限制API密钥的使用范围以提高安全性

### 2. 系统要求

- Node.js 14.0.0 或更高版本
- npm 6.0.0 或更高版本
- 支持HTTPS的Web服务器（生产环境）

## 🚀 部署方式

### 方式一：本地开发部署

#### 1. 克隆或下载项目文件

```bash
# 如果使用Git
git clone <repository-url>
cd google-translate-web-app

# 或者直接下载所有文件到项目目录
```

#### 2. 安装依赖

```bash
npm install
```

#### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入您的API密钥
nano .env
```

在`.env`文件中设置：
```env
GOOGLE_TRANSLATE_API_KEY=your_actual_api_key_here
PORT=3000
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
NODE_ENV=development
```

#### 4. 启动后端服务

```bash
# 开发模式（自动重启）
npm run dev

# 或生产模式
npm start
```

#### 5. 访问应用

打开浏览器访问：`http://localhost:3000`

### 方式二：生产环境部署

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js（Ubuntu/Debian）
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 2. 部署应用

```bash
# 创建应用目录
sudo mkdir -p /var/www/translate-app
cd /var/www/translate-app

# 上传项目文件（使用scp、git或其他方式）
# 例如使用git：
git clone <your-repository-url> .

# 安装依赖
npm install --production

# 配置环境变量
sudo nano .env
```

生产环境`.env`配置：
```env
GOOGLE_TRANSLATE_API_KEY=your_actual_api_key_here
PORT=3000
ALLOWED_ORIGINS=https://yourdomain.com
NODE_ENV=production
LOG_LEVEL=warn
```

#### 3. 使用PM2管理进程

```bash
# 安装PM2
sudo npm install -g pm2

# 启动应用
pm2 start server.js --name "translate-app"

# 设置开机自启
pm2 startup
pm2 save

# 查看状态
pm2 status
pm2 logs translate-app
```

#### 4. 配置Nginx反向代理

```bash
# 安装Nginx
sudo apt install nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/translate-app
```

Nginx配置：
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 静态文件
    location / {
        root /var/www/translate-app;
        try_files $uri $uri/ /translate.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

启用配置：
```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/translate-app /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

#### 5. 配置SSL证书（Let's Encrypt）

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 方式三：Docker部署

#### 1. 创建Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用文件
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 更改文件所有者
RUN chown -R nextjs:nodejs /app
USER nextjs

EXPOSE 3000

CMD ["npm", "start"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  translate-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - GOOGLE_TRANSLATE_API_KEY=${GOOGLE_TRANSLATE_API_KEY}
      - NODE_ENV=production
      - ALLOWED_ORIGINS=https://yourdomain.com
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - translate-app
    restart: unless-stopped
```

#### 3. 部署

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔧 配置优化

### 1. 性能优化

```javascript
// 在server.js中添加缓存
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 }); // 10分钟缓存

// 缓存翻译结果
app.post('/api/translate', async (req, res) => {
    const cacheKey = `${req.body.text}_${req.body.source}_${req.body.target}`;
    const cached = cache.get(cacheKey);
    
    if (cached) {
        return res.json(cached);
    }
    
    // ... 执行翻译
    // 缓存结果
    cache.set(cacheKey, result);
});
```

### 2. 监控和日志

```bash
# 安装监控工具
npm install winston morgan

# 配置日志
const winston = require('winston');
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.json(),
    transports: [
        new winston.transports.File({ filename: 'error.log', level: 'error' }),
        new winston.transports.File({ filename: 'combined.log' })
    ]
});
```

### 3. 安全加固

```javascript
// 添加更多安全中间件
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');

// 速度限制
const speedLimiter = slowDown({
    windowMs: 15 * 60 * 1000, // 15分钟
    delayAfter: 2, // 2次请求后开始延迟
    delayMs: 500 // 每次增加500ms延迟
});

app.use('/api/', speedLimiter);
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   # 检查环境变量
   echo $GOOGLE_TRANSLATE_API_KEY
   
   # 检查API密钥权限
   curl "https://translation.googleapis.com/language/translate/v2/languages?key=YOUR_API_KEY"
   ```

2. **CORS错误**
   - 确保ALLOWED_ORIGINS配置正确
   - 检查前端请求地址是否正确

3. **SSL证书问题**
   ```bash
   # 检查证书状态
   sudo certbot certificates
   
   # 手动续期
   sudo certbot renew
   ```

4. **服务无法启动**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :3000
   
   # 检查PM2状态
   pm2 status
   pm2 logs translate-app
   ```

### 监控命令

```bash
# 系统资源监控
htop

# 应用日志
pm2 logs translate-app --lines 100

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

## 📊 维护

### 定期维护任务

1. **更新依赖**
   ```bash
   npm audit
   npm update
   ```

2. **备份数据**
   ```bash
   # 备份配置文件
   tar -czf backup-$(date +%Y%m%d).tar.gz .env nginx.conf
   ```

3. **监控API使用量**
   - 定期检查Google Cloud Console中的API使用情况
   - 设置使用量警报

4. **性能监控**
   - 监控响应时间
   - 检查错误率
   - 分析用户访问模式

通过以上部署指南，您可以成功部署一个功能完整、安全可靠的Google Translate在线翻译工具。
